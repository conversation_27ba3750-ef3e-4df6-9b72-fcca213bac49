import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import "../../styles/Footer.css";
import logo from "../../assets/images/XOsports-hub-logo.png";
import { FaEnvelope, FaFacebookF } from "react-icons/fa";
import { FaSquareXTwitter } from "react-icons/fa6";
import { AiFillInstagram } from "react-icons/ai";
import { getPublishedCMSPages } from "../../services/cmsService";
import { usePublicSettings } from "../../hooks/useSettings";
import { updateFilters } from "../../redux/slices/buyerDashboardSlice";
import { getStoredUser } from "../../services/authService";
import {
  handleBuyNavigation,
  handleSellNavigation,
  getEffectiveUserRole,
  isUserAuthenticated,
} from "../../utils/navigationUtils";

const Footer = () => {
  const [cmsPages, setCmsPages] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Get site settings
  const { siteName, getSiteLogoUrl, contactEmail, publicSettings } =
    usePublicSettings();

  // Get user authentication and role information
  const isAuthenticated = isUserAuthenticated();
  const effectiveRole = getEffectiveUserRole();

  // Helper function to check if a social link is valid
  const isValidSocialLink = (link) => {
    return (
      link &&
      typeof link === "string" &&
      link.trim() !== "" &&
      link !== "undefined" &&
      link !== "null"
    );
  };

  // Handle sport link clicks with role-based redirection
  const handleSportClick = (e, sportName) => {
    e.preventDefault();

    // Get user data from localStorage
    const user = getStoredUser();

    if (!user) {
      // If no user is authenticated, redirect to auth page
      navigate("/auth");
      return;
    }

    // Get the active role (for non-admin users, use activeRole; for admin, use role)
    const activeRole = user.role === "admin" ? user.role : user.activeRole || user.role;

    // Only redirect buyers to content with sport filter
    if (activeRole === "buyer") {
      // Update the sport filter in redux
      dispatch(updateFilters({
        section: 'strategies',
        filters: { sport: sportName }
      }));

      // Navigate to content page with sport parameter
      navigate(`/content?sport=${encodeURIComponent(sportName)}`);
    } else {
      // For other roles, redirect to auth
      navigate("/auth");
    }
  };

  // Handle marketplace navigation clicks with role-based redirection
  const handleMarketplaceClick = (e, action) => {
    e.preventDefault();

    switch (action) {
      case 'signup':
      case 'login':
        navigate("/auth");
        break;
      case 'buy':
        handleBuyNavigation(navigate);
        break;
      case 'sell':
        handleSellNavigation(navigate);
        break;
      default:
        navigate("/auth");
    }
  };

  // Fetch published CMS pages on component mount
  useEffect(() => {
    const fetchCMSPages = async () => {
      try {
        setLoading(true);
        const response = await getPublishedCMSPages();
        if (response.success && Array.isArray(response.data)) {
          // Filter out any invalid pages and sort by title
          const validPages = response.data
            .filter((page) => page && page.title && page.slug)
            .sort((a, b) => a.title.localeCompare(b.title));
          setCmsPages(validPages);
        } else {
          setCmsPages([]);
        }
      } catch (error) {
        // Silently handle errors to prevent footer from breaking
        console.error("Error fetching CMS pages for footer:", error);
        setCmsPages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCMSPages();
  }, []);

  return (
    <footer className="footer-component footer">
      <div className="footer-container max-container">
        <div className="footer-logo-section">
          <img
            src={getSiteLogoUrl() || logo}
            alt={siteName || "XO Sports Hub"}
            className="footer-logo"
          />
          <p className="footer-tagline">
            "Elevate Your Game - A Digital Exchange of Sports Strategies"
          </p>
        </div>

        <div className="footer-links">
          <h3>Quick Links</h3>
          <ul className="quickLinks">
            <li>
              <Link to="/">Home</Link>
            </li>

            <li>
              <Link to="/contact">Contact Us</Link>
            </li>

            {/* Loading state for CMS pages */}
            {loading && (
              <li>
                <span
                  style={{
                    color: "var(--secondary-color)",
                    fontSize: "var(--smallfont)",
                  }}
                >
                  Loading pages...
                </span>
              </li>
            )}

            {/* Dynamically render published CMS pages (limit to 5 for footer) */}
            {!loading &&
              cmsPages.length > 0 &&
              cmsPages
                .slice(0, 5) // Limit to 5 pages to keep footer clean
                .map((page) => (
                  <li key={page._id || page.id}>
                    <Link
                      to={`/cms/${page.slug}`}
                      title={page.metaDescription || page.title}
                    >
                      {page.title}
                    </Link>
                  </li>
                ))}
          </ul>
        </div>

        <div className="footer-sports">
          <div className="footer-sports-grid">
            <div className="footer-sports-column">
              <h3>Explore a Sport</h3>

              <ul>
                <li>
                  <a
                    href="#"
                    onClick={(e) => handleSportClick(e, "Football")}
                    style={{ cursor: "pointer" }}
                  >
                    Football
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    onClick={(e) => handleSportClick(e, "Baseball")}
                    style={{ cursor: "pointer" }}
                  >
                    Baseball
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    onClick={(e) => handleSportClick(e, "Basketball")}
                    style={{ cursor: "pointer" }}
                  >
                    Basketball
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    onClick={(e) => handleSportClick(e, "Soccer")}
                    style={{ cursor: "pointer" }}
                  >
                    Soccer
                  </a>
                </li>
              </ul>
            </div>

            <div className="footer-links">
              <h3>Marketplace</h3>
              <ul className="quickLinks">
                {/* Show signup/login links for visitors */}
                {effectiveRole === "visitor" && (
                  <>
                    <li>
                      <a
                        href="#"
                        onClick={(e) => handleMarketplaceClick(e, 'signup')}
                        style={{ cursor: "pointer" }}
                      >
                        Signup
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        onClick={(e) => handleMarketplaceClick(e, 'login')}
                        style={{ cursor: "pointer" }}
                      >
                        Login
                      </a>
                    </li>
                  </>
                )}

                {/* Always show Buy/Sell links with role-based redirection */}
                <li>
                  <a
                    href="#"
                    onClick={(e) => handleMarketplaceClick(e, 'buy')}
                    style={{ cursor: "pointer" }}
                  >
                    Buy a Strategy
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    onClick={(e) => handleMarketplaceClick(e, 'sell')}
                    style={{ cursor: "pointer" }}
                  >
                    Sell a Strategy
                  </a>
                </li>
              </ul>
            </div>
            <div className="footer-contact">
              <div className="Contact__social-icons">
                {/* Facebook Link */}
                {isValidSocialLink(publicSettings?.socialLinks?.facebook) && (
                  <a
                    href={publicSettings.socialLinks.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="footericonborder"
                  >
                    <FaFacebookF className="footer__social-icon" />
                  </a>
                )}

                {/* Instagram Link */}
                {isValidSocialLink(publicSettings?.socialLinks?.instagram) && (
                  <a
                    href={publicSettings.socialLinks.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="footericonborder"
                  >
                    <AiFillInstagram className="footer__social-icon" />
                  </a>
                )}

                {/* Twitter Link */}
                {isValidSocialLink(publicSettings?.socialLinks?.twitter) && (
                  <a
                    href={publicSettings.socialLinks.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="footericonborder"
                  >
                    <FaSquareXTwitter className="footer__social-icon" />
                  </a>
                )}

                {/* Fallback: Show message if no social links */}
                {publicSettings &&
                  !isValidSocialLink(publicSettings?.socialLinks?.facebook) &&
                  !isValidSocialLink(publicSettings?.socialLinks?.instagram) &&
                  !isValidSocialLink(publicSettings?.socialLinks?.twitter) && (
                    <p style={{ color: "white", fontSize: "12px" }}>
                      Configure social media links in Admin Settings
                    </p>
                  )}
              </div>
              <h3 className="mt-30">Get in Touch</h3>
              <p>
                <FaEnvelope size={18} />
                <a
                  href={`mailto:${contactEmail || "<EMAIL>"}`}
                  style={{ color: "inherit", textDecoration: "none" }}
                >
                  {contactEmail || "<EMAIL>"}
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="footer-bottom max-container">
        <p>
          &copy; {new Date().getFullYear()} Sports Playbook Strategy
          Marketplace. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default Footer;
